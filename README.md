项目主要实现了以下功能：

主要功能分析
1. Shapefile 到 MongoDB 数据转换工具 ( 入库mangodb\shpToGeojson.py)
这是项目的核心功能，主要包含：

坐标系转换功能
提供了多种坐标系之间的转换，包括：
WGS84 ↔ GCJ02 (火星坐标系)
WGS84 ↔ BD09 (百度坐标系)
WGS84 ↔ Web墨卡托
WGS84 ↔ 西安80坐标系
百度墨卡托 ↔ 百度经纬度
数据处理流程
读取Shapefile文件 - 支持GBK和UTF-8编码
转换为GeoJSON格式 - 处理点、线、面等几何类型
坐标系转换 - 根据配置文件进行相应的坐标转换
数据入库 - 将处理后的数据存储到MongoDB数据库
特殊处理
自动检测文件编码（通过.cpg文件）
处理日期类型数据转换
为不同类型的网格数据添加特定字段（如gr_code、gr_name等）
将Polygon转换为MultiPolygon格式
2. 文件名处理工具 ( 去掉中文和特殊符号\EditFileName.py)
文件名清理功能
去除中文字符 - 将包含中文的文件名进行处理
批量重命名 - 根据配置文件批量修改文件名
去除特殊符号 - 移除括号及括号内的内容

总结
这个项目是一个GIS数据处理工具集，主要用于：

地理数据格式转换 - 将Shapefile转换为GeoJSON并存储到MongoDB
坐标系统转换 - 支持中国常用的多种坐标系转换
文件管理 - 批量处理文件名，去除中文和特殊字符
数据库集成 - 自动化地将地理数据导入MongoDB数据库
这个工具特别适用于中国的GIS项目，因为它专门处理了中国特有的坐标系转换问题（如火星坐标系、百度坐标系等）。