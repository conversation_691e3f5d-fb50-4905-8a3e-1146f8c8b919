 # -*- coding: utf-8 -*-
"""
Created on Tu June 20 10:54:46 2017
 
@author: wa<PERSON><PERSON><PERSON>
@Function:批量更改文件名

to calculate size after filter in conv and pool
"""
import os.path, time
import os
import glob
import subprocess
import re
import sys
import json
import math

rootdir =os.path.split(os.path.realpath(__file__))[0]
isRemoveChinese=True
isTH=True
oldWord="（.*?）"
newWord=""
class EditFileName(object):
    def __init__(self,obj):
        self.obj=obj      
        self.readConfig()          
       
    #读取配置文件   
    def readConfig(self):
        with open(self.obj, 'r') as f:
            self.temp = json.loads(f.read())
            self.oldName=self.temp["oldName"]
            self.newName=self.temp["newName"]
            for fileNmae in self.temp["fileType"]:
                self.Edit(fileNmae)

    def Edit(self,fileName):
        for file_name in glob.glob("*."+fileName):
            shpname=file_name[0:len(file_name)-4]           
            if self.is_chinese(shpname) and isRemoveChinese:              
                name=self.cf(shpname)
                name=str(name)           
                self.updateName(rootdir,shpname,name)    
            if	isTH:           
                self.updateName(rootdir,self.oldName,self.newName)	

    #更改文件名称
    def updateName(self,path,oldName,newName):
        file_dir=r''+path+''
        file_match=[]
        old=oldName
        new=newName
        os.chdir(file_dir)
        for file_list in os.listdir(file_dir):
            if file_list.find(old) != -1:
                file_match.append(os.path.join(file_dir,file_list))         
        for item in file_match:
            if item.find(old) !=1:
                new_file_name=item.replace(old,new)
                #去掉括号及括号内容
                new_file_name = re.sub(oldWord, newWord, new_file_name)               
                new_file=os.path.join(file_dir,new_file_name)
                os.rename(item,new_file)

    #将全部或部分有汉字的命名转为拼音后拼接
    def cf(self,words):
        AllWords=[]
        index=0
        for word in words:
            if self.is_chinese(word):                    
                if index==0:
                    lastWord=AllWords[len(AllWords)-1]
                    if lastWord == "_":
                        AllWords.pop()
                index=index+1
                pass
            else:
                AllWords.append(word)
        result1="".join(AllWords)
        return result1

    #识别字符串有没有中文
    def is_chinese(self,words):
        zhPattern = re.compile(u'[\u4e00-\u9fa5]+')
        match = zhPattern.search(words)
        if match:
            return True
        else:
            return False
   
newAreaName=EditFileName("editNameConfig.json")

