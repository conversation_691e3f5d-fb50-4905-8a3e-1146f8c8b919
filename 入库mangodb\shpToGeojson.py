import shapefile
import os.path, time
import os
import glob
import json
import codecs
import pymongo
import math
from tqdm import *
from threading import Timer
import datetime
class transform(object):
    x_PI = 3.14159265358979324 * 3000.0 / 180.0
    PI = 3.1415926535897932384626
    a = 6378245.0
    ee = 0.00669342162296594323

    #wgs84转web墨卡托
    #WGS-84 to Web mercator
    #mercatorLat -> y mercatorLon -> x
    @staticmethod
    def mercator_encrypt(wgsLon,wgsLat):
        print(wgsLon)
        print(wgsLat)
        x = wgsLon * 20037508.34 / 180.0
        y = math.log(math.tan((90.0 + wgsLat) * transform.PI / 360.0)) / (transform.PI / 180.0)
        y = y * 20037508.34 / 180.0
        return [x,y]


    #web墨卡托转wgs84
    #Web mercator to WGS-84
    #mercatorLat -> y mercatorLon -> x
    @staticmethod
    def mercator_decrypt(mercatorLon,mercatorLat):
        x = mercatorLon / 20037508.34 * 180.0;
        y = mercatorLat / 20037508.34 * 180.0;
        y = 180/transform.PI * (2 * math.atan(math.exp(y * transform.PI / 180.0)) - transform.PI / 2);
        return [x,y];

    @staticmethod
    def bd09togcj02(bd_lon, bd_lat):
        x_pi = 3.14159265358979324 * 3000.0 / 180.0
        x = bd_lon - 0.0065
        y = bd_lat - 0.006
        z = math.sqrt(x * x + y * y) - 0.00002 * math.sin(y * x_pi)
        theta = math.atan2(y, x) - 0.000003 * math.cos(x * x_pi)
        gg_lng = z * math.cos(theta)
        gg_lat = z * math.sin(theta)
        return [gg_lng, gg_lat]

    ##
    # 火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换
    # 即谷歌、高德 转 百度
    # @param lng
    # @param lat
    # @returns {*[]}
    ##

    @staticmethod
    def gcj02tobd09 (lng, lat) :
        z = math.sqrt(lng * lng + lat * lat) + 0.00002 * math.sin(lat * transform.x_PI)
        theta = math.atan2(lat, lng) + 0.000003 * math.cos(lng * transform.x_PI)
        bd_lng = z * math.cos(theta) + 0.0065
        bd_lat = z * math.sin(theta) + 0.006
        return [bd_lng, bd_lat]

    ##
    # WGS84转GCj02
    # @param lng
    # @param lat
    # @returns {*[]}
    ##
    @staticmethod
    def wgs84togcj02(lng, lat):
        dlat = transform.transformlat(lng - 105.0, lat - 35.0)
        dlng = transform.transformlng(lng - 105.0, lat - 35.0)
        radlat = lat / 180.0 * transform.PI
        magic = math.sin(radlat)
        magic = 1 - transform.ee * magic * magic
        sqrtmagic = math.sqrt(magic)
        dlat = (dlat * 180.0) / ((transform.a * (1 - transform.ee)) / (magic * sqrtmagic) * transform.PI)
        dlng = (dlng * 180.0) / (transform.a / sqrtmagic * math.cos(radlat) * transform.PI)
        mglat = lat + dlat
        mglng = lng + dlng
        return [mglng, mglat]


    #百度经纬度转wgs84
    @staticmethod
    def bd09towgs84(lng,lat):
        gcj02 = transform.bd09togcj02(lng,lat)
        return transform.gcj02towgs84(gcj02[0], gcj02[1])

    #wgs84转百度经纬度
    @staticmethod
    def wgs84tobd09(lng, lat):
        gcj02 = transform.wgs84togcj02(lng, lat)
        return transform.gcj02tobd09(gcj02[0], gcj02[1])
    
    @staticmethod	
    def wgs84toGD(lng, lat):
        gcj02 = transform.wgs84togcj02(lng, lat)
        return transform.mercator_encrypt(gcj02[0], gcj02[1])
    
    @staticmethod	
    def GDtowgs84(lng, lat):
        gcj02 = transform.mercator_decrypt(lng, lat)
        return transform.gcj02towgs84(gcj02[0], gcj02[1])
    
    @staticmethod	
    def bd09toGD(lng, lat):
        gcj02 = transform.bd09togcj02(lng, lat)
        return transform.mercator_encrypt(gcj02[0], gcj02[1])
    
    @staticmethod	
    def GDtobd09(lng, lat):
        gcj02 = transform.mercator_decrypt(lng, lat)
        return transform.gcj02tobd09(gcj02[0], gcj02[1])

    ##
    # GCJ02 转换为 WGS84
    # @param lng
    # @param lat
    # @returns {*[]}
    ##

    @staticmethod
    def gcj02towgs84(lng, lat):
        dlat = transform.transformlat(lng - 105.0, lat - 35.0)
        dlng = transform.transformlng(lng - 105.0, lat - 35.0)
        radlat = lat / 180.0 * transform.PI
        magic = math.sin(radlat)
        magic = 1 - transform.ee * magic * magic
        sqrtmagic = math.sqrt(magic)
        dlat = (dlat * 180.0) / ((transform.a * (1 - transform.ee)) / (magic * sqrtmagic) * transform.PI)
        dlng = (dlng * 180.0) / (transform.a / sqrtmagic * math.cos(radlat) * transform.PI)
        mglat = lat + dlat
        mglng = lng + dlng
        return [lng * 2 - mglng, lat * 2 - mglat]

    @staticmethod
    def transformlat(lng, lat):
        ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * math.sqrt(abs(lng))
        ret += (20.0 * math.sin(6.0 * lng * transform.PI) + 20.0 * math.sin(2.0 * lng * transform.PI)) * 2.0 / 3.0
        ret += (20.0 * math.sin(lat * transform.PI) + 40.0 * math.sin(lat / 3.0 * transform.PI)) * 2.0 / 3.0
        ret += (160.0 * math.sin(lat / 12.0 * transform.PI) + 320 * math.sin(lat * transform.PI / 30.0)) * 2.0 / 3.0
        return ret

    @staticmethod
    def transformlng(lng, lat):
        ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * math.sqrt(abs(lng))
        ret += (20.0 * math.sin(6.0 * lng * transform.PI) + 20.0 * math.sin(2.0 * lng * transform.PI)) * 2.0 / 3.0
        ret += (20.0 * math.sin(lng * transform.PI) + 40.0 * math.sin(lng / 3.0 * transform.PI)) * 2.0 / 3.0
        ret += (150.0 * math.sin(lng / 12.0 * transform.PI) + 300.0 * math.sin(lng / 30.0 * transform.PI)) * 2.0 / 3.0
        return ret

    ##
    # 判断是否在国内，不在国内则不做偏移
    # @param lng
    # @param lat
    # @returns {boolean}
    ##

    @staticmethod
    def out_of_china(lng, lat):
        return (lng < 72.004 or lng > 137.8347) or ((lat < 0.8293 or lat > 55.8271) or false)


    #百度墨卡托转百度wgs84
    EARTHRADIUS = 6370996.81
    MCBAND = [12890594.86, 8362377.87, 5591021, 3481989.83, 1678043.12, 0];
    LLBAND = [75, 60, 45, 30, 15, 0];
    MC2LL = [[1.410526172116255e-8, 0.00000898305509648872, -1.9939833816331, 200.9824383106796, -187.2403703815547, 91.6087516669843, -23.38765649603339, 2.57121317296198, -0.03801003308653, 17337981.2], [-7.435856389565537e-9, 0.000008983055097726239, -0.78625201886289, 96.32687599759846, -1.85204757529826, -59.36935905485877, 47.40033549296737, -16.50741931063887, 2.28786674699375, 10260144.86], [-3.030883460898826e-8, 0.00000898305509983578, 0.30071316287616, 59.74293618442277, 7.357984074871, -25.38371002664745, 13.45380521110908, -3.29883767235584, 0.32710905363475, 6856817.37], [-1.981981304930552e-8, 0.000008983055099779535, 0.03278182852591, 40.31678527705744, 0.65659298677277, -4.44255534477492, 0.85341911805263, 0.12923347998204, -0.04625736007561, 4482777.06], [3.09191371068437e-9, 0.000008983055096812155, 0.00006995724062, 23.10934304144901, -0.00023663490511, -0.6321817810242, -0.00663494467273, 0.03430082397953, -0.00466043876332, 2555164.4], [2.890871144776878e-9, 0.000008983055095805407, -3.068298e-8, 7.47137025468032, -0.00000353937994, -0.02145144861037, -0.00001234426596, 0.00010322952773, -0.00000323890364, 826088.5]];
    LL2MC = [[-0.0015702102444, 111320.7020616939, 1704480524535203, -10338987376042340, 26112667856603880, -35149669176653700, 26595700718403920, -10725012454188240, 1800819912950474, 82.5], [0.0008277824516172526, 111320.7020463578, 647795574.6671607, -4082003173.641316, 10774905663.51142, -15171875531.51559, 12053065338.62167, -5124939663.577472, 913311935.9512032, 67.5], [0.00337398766765, 111320.7020202162, 4481351.045890365, -23393751.19931662, 79682215.47186455, -115964993.2797253, 97236711.15602145, -43661946.33752821, 8477230.501135234, 52.5], [0.00220636496208, 111320.7020209128, 51751.86112841131, 3796837.749470245, 992013.7397791013, -1221952.21711287, 1340652.697009075, -620943.6990984312, 144416.9293806241, 37.5], [-0.0003441963504368392, 111320.7020576856, 278.2353980772752, 2485758.690035394, 6070.750963243378, 54821.18345352118, 9540.606633304236, -2710.55326746645, 1405.483844121726, 22.5], [-0.0003218135878613132, 111320.7020701615, 0.00369383431289, 823725.6402795718, 0.46104986909093, 2351.343141331292, 1.58060784298199, 8.77738589078284, 0.37238884252424, 7.45]];

    ##
    # 百度墨卡托转百度经纬度
    # @param lng
    # @param lat
    # @returns {*[]}
    ##

    @staticmethod
    def bd_mkt2bd_wgs(lng, lat):
        cF = null
        lng = math.abs(lng)
        lat = math.abs(lat)
        for i in range(len(MCBAND)):
            if lat >= MCBAND[cE]:
                cF = MC2LL[cE]
                break;
        
        lng = cF[0] + cF[1] * math.abs(lng)
        cC = math.abs(lat) / cF[9]
        lat = cF[2] + cF[3] * cC + cF[4] * cC * cC + cF[5] * cC * cC * cC + cF[6] * cC * cC * cC * cC + cF[7] * cC * cC * cC * cC * cC + cF[8] * cC * cC * cC * cC * cC * cC
        lng *= -1 if lng < 0 else 1
        lat *= -1 if lat < 0 else 1
        return [lng, lat]

    ##
    # 百度经纬度转百度墨卡托
    # @param lng
    # @param lat
    # @returns {*[]}
    ##

    @staticmethod
    def bd_wgs2bd_mkt(lng, lat):
        cF = null
        lng = getLoop(lng, -180, 180)
        lat = getRange(lat, -74, 74)
        for i in range(len(LLBAND)):
            if lat >= LLBAND[i]:
                cF = LL2MC[i]
                break

        if cF != null:#i in range(len(LLBAND)-1,-1,-1)
            for i in range(len(LLBAND)-1,-1,-1):
                if lat <= -LLBAND[i]:
                    cF = LL2MC[i]
                    break;
        lng = cF[0] + cF[1] * math.abs(lng)
        cC = math.abs(lat) / cF[9]
        lat = cF[2] + cF[3] * cC + cF[4] * cC * cC + cF[5] * cC * cC * cC + cF[6] * cC * cC * cC * cC + cF[7] * cC * cC * cC * cC * cC + cF[8] * cC * cC * cC * cC * cC * cC
        lng *= -1 if lng < 0 else 1
        lat *= -1 if lat < 0 else 1
        return [lng, lat]

    
        
    @staticmethod
    def wgs84toXiAn80(longitude, latitude):
        L0 = 116
        PI=3.1415926535897932384626  
        iPI = 0.0174532925199433
        a = 6378137.0
        f = 1 / 298.257222101
        longitude0 = (L0 / 180.0) * PI
        longitude1 = longitude * iPI
        latitude1 = latitude * iPI
        e2 = 2 * f - f * f
        ee = e2 * (1.0 - e2)
        NN = a / math.sqrt(1.0 - e2 * math.sin(latitude1) * math.sin(latitude1))
        T = math.tan(latitude1) * math.tan(latitude1)
        C = ee * math.cos(latitude1) * math.cos(latitude1)
        A = (longitude1 - longitude0) * math.cos(latitude1)
        M = a * ((1 - e2 / 4 - 3 * e2 * e2 / 64 - 5 * e2 * e2 * e2 / 256) * latitude1 - (3 * e2 / 8 + 3 * e2 * e2 / 32 + 45 * e2 * e2
            * e2 / 1024) * math.sin(2 * latitude1) + (15 * e2 * e2 / 256 + 45 * e2 * e2 * e2 / 1024) * math.sin(4 * latitude1) - (35 * e2 * e2 * e2 / 3072) * math.sin(6 * latitude1))
        xval = NN * (A + (1 - T + C) * A * A * A / 6 + (5 - 18 * T + T * T + 72 * C - 58 * ee) * A * A * A * A * A / 120)
        yval = M + NN * math.tan(latitude1) * (A * A / 2 + (5 - T + 9 * C + 4 * C * C) * A * A * A * A / 24
            + (61 - 58 * T + T * T + 600 * C - 330 * ee) * A * A * A * A * A * A / 720)
        X0 = 500000
        Y0 = 0
        X54_80 = xval + X0
        Y54_80 = yval + Y0
        X54_80 = X54_80 -30526.167675068602
        Y54_80 = Y54_80 -4114871.0398816783
        return [X54_80, Y54_80]

    @staticmethod
    def xiAn80toWGS84(X54_80, Y54_80):
        L0 = 116;
        PI=3.1415926535897932384626  
        X54_80 = X54_80 + 30526.167675068602;
        Y54_80 = Y54_80 + 4114871.0398816783;
        iPI = 0.0174532925199433;
        a = 6378137.0; f = 1 / 298.257222101;
        longitude0 = (L0 / 180.0) * PI; 
        X0 = 500000;
        Y0 = 0;
        xval = X54_80 - X0; yval = Y54_80 - Y0;
        e2 = 2 * f - f * f;
        e1 = (1.0 - math.sqrt(1 - e2)) / (1.0 + math.sqrt(1 - e2));
        ee = e2 / (1 - e2);
        M = yval;
        u = M / (a * (1 - e2 / 4 - 3 * e2 * e2 / 64 - 5 * e2 * e2 * e2 / 256));
        fai = u + (3 * e1 / 2 - 27 * e1 * e1 * e1 / 32) * math.sin(2 * u) + (21 * e1 * e1 / 16 - 55 * e1 * e1 * e1 * e1 / 32) * math.sin(
                4 * u) + (151 * e1 * e1 * e1 / 96) * math.sin(6 * u) + (1097 * e1 * e1 * e1 * e1 / 512) * math.sin(8 * u);
        C = ee * math.cos(fai) * math.cos(fai)
        T = math.tan(fai) * math.tan(fai)
        NN = a / math.sqrt(1.0 - e2 * math.sin(fai) * math.sin(fai))
        R = a * (1 - e2) / math.sqrt((1 - e2 * math.sin(fai) * math.sin(fai)) * (1 - e2 * math.sin(fai) * math.sin(fai)) * (1 - e2 * math.sin
                (fai) * math.sin(fai)))
        D = xval / NN
        longitude1 = longitude0 + (D - (1 + 2 * T + C) * D * D * D / 6 + (5 - 2 * C + 28 * T - 3 * C * C + 8 * ee + 24 * T * T) * D
            * D * D * D * D / 120) / math.cos(fai);
        latitude1 = fai - (NN * math.tan(fai) / R) * (D * D / 2 - (5 + 3 * T + 10 * C - 4 * C * C - 9 * ee) * D * D * D * D / 24
            + (61 + 90 * T + 298 * C + 45 * T * T - 256 * ee - 3 * C * C) * D * D * D * D * D * D / 720)
        longitude = longitude1 / iPI
        latitude = latitude1 / iPI
        return [longitude, latitude]


    #print(len(buffer))
    #print('转换成功！')

'''class MigrationTransformer(pymongo.son_manipulator.SONManipulator):
    def _encode_date(self, value):
        return datetime.datetime.combine(
                value,
                datetime.datetime.min.time())
    def transform_incoming(self, son, collection):
        for (key, value) in son.items():
            # datetime.datetime is instance of datetime.date
            # compare type explicitly only
            if type(value) == datetime.date:
                son[key] = self._encode_date(value)
            elif isinstance(value, dict):    # recurse into sub-docs
                son[key] = self.transform_incoming(value, collection)
        return son'''
    
class updateXY(object):
    def __init__(self,obj):
        self.obj=obj
        self.dbsInfo=dict()
        self.readConfig()
        
    def closeWin(self):
        print("关闭！")
       
    #读取配置文件   
    def readConfig(self):
        with open(self.obj, 'r') as f:
            self.temp = json.loads(f.read())
            self.dealWithAllDbs()
            #print("更新完成完成！")
            t = Timer(5.0, self.closeWin) 
            t.start()

        #链接数据库
    def connectDb(self,dbName):
        dbInfo=dict()
        myclient = pymongo.MongoClient('mongodb://localhost:27017/')
        #pymongo.MongoClient(host='xxxxx', port=xxxx, username='xxxxx', password='xxxx',authSource='xxxxx')
        # myclient = pymongo.MongoClient('mongodb://用户名:密码@host:port/')
        mydb = myclient["YaoAn"] # mysql的database
        #mycol = mydb["comment"] # mysql的table
        #mydb.add_son_manipulator(MigrationTransformer())
        dbInfo["collection"]=myclient
        dbInfo["database"]=mydb
        #dbInfo["cursor"]=cur
        self.dbsInfo[dbName]=dbInfo

    def is_date(self,obj):
        return isinstance(obj, datetime.date)
    def Shp2JSON(self,filename,shp_encoding='utf-8',json_encoding='utf-8'):
        '''
        这个函数用于将shp文件转换为GeoJSON文件
        :param filename: shp文件对应的文件名（去除文件拓展名）
        :return:
        '''
     
        '''创建shp IO连接'''
        reader = shapefile.Reader(filename,encoding=shp_encoding)
     
        '''提取所有field部分内容'''
        fields = reader.fields[1:]
     
        '''提取所有field的名称'''
        field_names = [field[0] for field in fields]
     
        '''初始化要素列表'''
        buffer = []
     
        for sr in tqdm(reader.shapeRecords()):
            
            '''提取每一个矢量对象对应的属性值'''
            record = sr.record
     
            '''属性转换为列表'''
            record = [r.decode('gb2312','ignore') if isinstance(r, bytes)
                      else r for r in record]
     
            '''对齐属性与对应数值的键值对'''
            
            for index in range(0,len(field_names)):    
                field_names[index] = field_names[index].lower()
                
            atr = dict(zip(field_names, record))
     
            '''获取当前矢量对象的类型及矢量信息'''
            geom = sr.shape.__geo_interface__
           
            '''向要素列表追加新对象'''
            buffer.append(dict(type="Feature",
                               geometry=geom,
                               properties=atr))
        #将date类型转为字符，不然无法转为geojson
        for index in range(0,len(buffer)):
            for key in buffer[index]["properties"]:
                if self.is_date(buffer[index]["properties"][key]):
                    buffer[index]["properties"][key]=str(buffer[index]["properties"][key])
        #转换坐标系
        for index in range(0,len(buffer)):
            geometry=buffer[index]["geometry"]
            if geometry["type"]=="Point":
                points= geometry["coordinates"]
                point=self.transform(points[0],points[1])
                buffer[index]["geometry"]["coordinates"]=tuple(point)
            if geometry["type"]=="MultiPoint":
                points= geometry["coordinates"]
                for i in range(0,len(points)):
                    point=points[i]
                    pnt=self.transform(point[0],point[1])
                    geometry["coordinates"][i]=tuple(pnt)
            if geometry["type"]=="LineString":
                points= geometry["coordinates"]
                for i in range(0,len(points)):
                    point=points[i]
                    pnt=self.transform(point[0],point[1])
                    geometry["coordinates"][i]=tuple(pnt)
            if geometry["type"]=="MultiLineString":
                points= geometry["coordinates"]
                for i in range(0,len(points)):
                    line=points[i]
                    for j in range(0,len(line)):
                        point=line[j]
                        pnt=self.transform(point[0],point[1])
                        geometry["coordinates"][i][j]=tuple(pnt)
            if geometry["type"]=="Polygon":
                points= geometry["coordinates"]
                for i in range(0,len(points)):
                    line=points[i]
                    for j in range(0,len(line)):
                        point=line[j]
                        pnt=self.transform(point[0],point[1])
                        geometry["coordinates"][i][j]=tuple(pnt)

            if geometry["type"]=="MultiPolygon":
                coordinates= geometry["coordinates"]
                for i in range(0,len(coordinates)):
                    polygon=coordinates[i]
                    for j in range(0,len(polygon)):
                        points=polygon[j]
                        for k in range(0,len(points)):
                            point=points[k]
                            pnt=self.transform(point[0],point[1])
                            geometry["coordinates"][i][j][k]=tuple(pnt)
        for index in range(0,len(buffer)):
             geometry=buffer[index]["geometry"]
             if geometry["type"]=="Polygon":
                 geometry["type"]="MultiPolygon"
                 geometry["coordinates"]=tuple([geometry["coordinates"]])
            
                
        '''写出GeoJSON文件'''
        geojson = codecs.open(filename + "-geo.json","w", encoding=json_encoding)
        geojson.write(json.dumps({"type":"FeatureCollection",                        "features":buffer}) + '\n')
        geojson.close()
        return buffer

    def transform(self,x,y):
        transformType=self.temp["transformType"]
        point=[x,y];
        if transformType == 1:
            point=transform.gcj02towgs84(x,y)
        elif transformType==2:
            point=transform.wgs84togcj02(x,y)
        elif transformType==3:
            point=transform.wgs84toGD(x,y)
        elif transformType==4:
            point=transform.GDtowgs84(x,y)
        elif transformType==5:
            point=transform.wgs84tobd09(x,y)
        elif transformType==6:
            point=transform.bd09towgs84(x,y)
        elif transformType==7:
            point=transform.GDtobd09(x,y)
        elif transformType==8:
            point=transform.bd09toGD(x,y)
        elif transformType==9:
            point=transform.mercator_encrypt(x,y)
        elif transformType==10:
            point=transform.mercator_decrypt(x,y)
        return point
        
    #关闭数据库
    def closeConnect(self,dbName):
        dbInfo=self.dbsInfo[dbName]
        dbInfo["collection"].close()
        #dbInfo["connect"].close()

    def _encode_date(self, value):
        return datetime.datetime.combine(
                value,
                datetime.datetime.min.time())

    def dealWithAllDbs(self):
        self.connectDb('mongoDb')
        dbInfo=self.dbsInfo['mongoDb']
        collection=dbInfo['database']['wh_sys']
        for shp_file_name in glob.glob("*.shp"):
            shpFileName=shp_file_name[0:len(shp_file_name)-4]
            print("正在导入数据源："+shp_file_name)
            dataCode='GBK'
            if os.path.exists(shpFileName+".cpg"):
                dataCode='utf-8'
            featureList=self.Shp2JSON(filename=shpFileName,
             shp_encoding=dataCode,
             json_encoding='utf-8')
            index=1
            for item in featureList:
                values=item['properties']
                for key in  values:
                    #不支持date 需要转一下
                    if type(values[key]) == datetime.date:
                        values[key]=self._encode_date(values[key])
                item['id']=shpFileName+'.'+str(index)+"new"
                item['properties']['type']=shpFileName
                if shpFileName=="T_MAP_DISTRICT_GRID":
                    item['properties']['gr_code']=str(int(item['properties']['qucode']))
                    item['properties']['gr_name']=item['properties']['quname']
                    item['properties']['gr_up_code']=str(int(item['properties']['qucode']))
                    item['properties']['gr_up_name']=item['properties']['quname']
                if shpFileName=="T_MAP_COMM_GRID":
                    item['properties']['gr_code']=str(int(item['properties']['sqcode']))
                    item['properties']['gr_name']=item['properties']['sqname']
                    item['properties']['gr_up_code']=str(int(item['properties']['jdcode']))
                    item['properties']['gr_up_name']=item['properties']['jdname']
                if shpFileName=="T_MAP_CUSTOM_WORKGRID":
                    item['properties']['gr_code']=str(item['properties']['zrcode'])
                    item['properties']['gr_name']=item['properties']['zrname']
                    item['properties']['gr_up_code']=str(item['properties']['sqcode'])
                    item['properties']['gr_up_name']=item['properties']['sqname']
                if shpFileName=="T_MAP_CUSTOM_WORKGRID":
                    item['properties']['gr_code']=str(item['properties']['zrcode'])
                    item['properties']['gr_name']=item['properties']['zrname']
                    item['properties']['gr_up_code']=str(item['properties']['zrcode'])
                    item['properties']['gr_up_name']=item['properties']['zrname']
                if shpFileName=="T_MAP_STREET_GRID":
                    item['properties']['gr_code']=str(int(item['properties']['jdcode']))
                    item['properties']['gr_name']=item['properties']['jdname']
                    item['properties']['gr_up_code']=str(int(item['properties']['qucode']))
                    item['properties']['gr_up_name']=item['properties']['quname']
                if shpFileName=="T_MAP_UNIT_GRID":
                    item['properties']['gr_code']=str(int(item['properties']['bgcode']))
                    item['properties']['gr_name']=item['properties']['bgcode']
                    item['properties']['gr_up_code']=str(int(item['properties']['sqcode']))
                    item['properties']['gr_up_name']=item['properties']['sqcode']
 
                collection.insert_one (item)
                #collection.create_index([("geom", pymongo.GEO2D)])
                index=index+1
        #collection.create_index([("geometry","2dsphere")])
        self.closeConnect('mongoDb')

if __name__ == '__main__':
    #import os
    #os.chdir(r'C:\Users\<USER>\Desktop\飞线图素材')
    updateXY("config.json")
    '''Shp2JSON(filename='street.shp',
             shp_encoding='gbk',
             json_encoding='utf-8')'''
